#!/usr/bin/env python3
"""
修复版VTK URDF查看器 - 解决多窗口OpenGL上下文冲突问题
"""

import sys
import os
import time
import numpy as np
import trimesh
import vtk
from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
from urdf_parser_py.urdf import URDF
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QSlider, QFrame,
                             QGroupBox, QScrollArea, QSplitter, QComboBox,
                             QColorDialog, QSpinBox, QDoubleSpinBox, QDialog,
                             QFileDialog, QCheckBox)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QColor
import xml.etree.ElementTree as ET
from typing import Dict, List, Tuple, Optional


class VTKContextManager:
    """VTK OpenGL上下文管理器"""
    
    _active_context = None
    _context_count = 0
    
    @classmethod
    def acquire_context(cls, render_window):
        """获取OpenGL上下文"""
        try:
            # 如果有活动上下文，先释放
            if cls._active_context and cls._active_context != render_window:
                cls._active_context.ReleaseCurrent()
            
            # 激活新上下文
            render_window.MakeCurrent()
            cls._active_context = render_window
            cls._context_count += 1
            
            print(f"获取OpenGL上下文 #{cls._context_count}")
            return True
            
        except Exception as e:
            print(f"获取OpenGL上下文失败: {e}")
            return False
    
    @classmethod
    def release_context(cls, render_window):
        """释放OpenGL上下文"""
        try:
            if cls._active_context == render_window:
                render_window.ReleaseCurrent()
                cls._active_context = None
                print("释放OpenGL上下文")
                
        except Exception as e:
            print(f"释放OpenGL上下文失败: {e}")


class FixedVTKURDFViewer(QFrame):
    """修复版VTK URDF查看器"""
    
    loading_finished = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 基本属性
        self.robot = None
        self.mesh_actors = {}
        self.joint_values = {}
        self.current_urdf_path = None
        
        # VTK相关
        self.vtk_widget = None
        self.renderer = None
        self.render_window = None
        self.interactor = None
        self.axes_actor = None
        
        # 窗口管理
        self.additional_windows = []
        
        self._setup_ui()
        self._setup_vtk_with_context_management()
    
    def _setup_ui(self):
        """设置UI布局"""
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 使用分割器创建左右布局
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧控制面板
        control_panel = self._create_control_panel()
        splitter.addWidget(control_panel)
        
        # 右侧VTK渲染窗口
        vtk_frame = QFrame()
        vtk_layout = QVBoxLayout(vtk_frame)
        vtk_layout.setContentsMargins(0, 0, 0, 0)
        
        self.vtk_widget = QVTKRenderWindowInteractor(vtk_frame)
        vtk_layout.addWidget(self.vtk_widget)
        
        # 底部控制按钮
        button_frame = QFrame()
        button_layout = QHBoxLayout(button_frame)
        
        self.load_button = QPushButton("加载URDF")
        self.load_button.clicked.connect(self.load_default_urdf)
        button_layout.addWidget(self.load_button)
        
        self.reset_button = QPushButton("重置视角")
        self.reset_button.clicked.connect(self.reset_camera)
        button_layout.addWidget(self.reset_button)
        
        self.new_window_button = QPushButton("新窗口")
        self.new_window_button.clicked.connect(self.open_new_window)
        button_layout.addWidget(self.new_window_button)
        
        vtk_layout.addWidget(button_frame)
        splitter.addWidget(vtk_frame)
        
        # 设置分割比例
        splitter.setSizes([300, 700])
        
        layout.addWidget(splitter)
        self.setLayout(layout)
    
    def _create_control_panel(self):
        """创建左侧控制面板"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumWidth(250)
        scroll_area.setMaximumWidth(350)
        
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        
        # 信息显示组
        info_group = QGroupBox("信息")
        info_layout = QVBoxLayout(info_group)
        
        self.info_label = QLabel("未加载模型")
        self.info_label.setWordWrap(True)
        info_layout.addWidget(self.info_label)
        
        control_layout.addWidget(info_group)
        
        # 添加弹性空间
        control_layout.addStretch()
        
        scroll_area.setWidget(control_widget)
        return scroll_area
    
    def _setup_vtk_with_context_management(self):
        """设置VTK渲染环境（带上下文管理）"""
        try:
            # 获取渲染窗口
            self.render_window = self.vtk_widget.GetRenderWindow()
            
            # 设置渲染窗口属性以避免OpenGL上下文冲突
            self.render_window.SetMultiSamples(0)  # 禁用多重采样
            self.render_window.SetAlphaBitPlanes(0)  # 禁用alpha通道
            self.render_window.SetStencilCapable(0)  # 禁用模板缓冲
            
            # 获取OpenGL上下文
            if not VTKContextManager.acquire_context(self.render_window):
                print("警告: 无法获取OpenGL上下文")
            
            # 创建渲染器
            self.renderer = vtk.vtkRenderer()
            self.renderer.SetBackground(0.2, 0.2, 0.3)
            self.render_window.AddRenderer(self.renderer)
            
            # 获取交互器
            self.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
            
            # 设置交互样式
            style = vtk.vtkInteractorStyleTrackballCamera()
            self.interactor.SetInteractorStyle(style)
            
            # 添加坐标轴
            self._add_coordinate_axes()
            
            # 添加光源
            self._setup_lighting()
            
            # 初始化交互器
            self.interactor.Initialize()
            
            # 确保渲染窗口正确初始化
            self.render_window.Render()
            
            print("VTK环境设置完成")
            
        except Exception as e:
            print(f"VTK设置失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _add_coordinate_axes(self):
        """添加坐标轴"""
        axes = vtk.vtkAxesActor()
        axes.SetTotalLength(0.05, 0.05, 0.05)  # 5cm
        axes.SetAxisLabels(1)
        axes.SetCylinderRadius(0.002)
        
        self.axes_actor = axes
        self.renderer.AddActor(axes)
    
    def _setup_lighting(self):
        """设置光照"""
        # 主光源
        light1 = vtk.vtkLight()
        light1.SetPosition(1, 1, 1)
        light1.SetFocalPoint(0, 0, 0)
        light1.SetIntensity(0.8)
        self.renderer.AddLight(light1)
        
        # 补光
        light2 = vtk.vtkLight()
        light2.SetPosition(-1, -1, 0.5)
        light2.SetFocalPoint(0, 0, 0)
        light2.SetIntensity(0.4)
        self.renderer.AddLight(light2)
    
    def load_default_urdf(self):
        """加载默认URDF文件"""
        urdf_path = "../brainco-lefthand-URDF-V2/urdf/brainco-lefthand-URDF-V2_converted.urdf"
        if os.path.exists(urdf_path):
            self.load_urdf(urdf_path)
        else:
            print(f"默认URDF文件不存在: {urdf_path}")
    
    def load_urdf(self, urdf_path):
        """加载URDF文件"""
        try:
            print(f"加载URDF文件: {urdf_path}")
            
            # 清除现有模型
            self.clear_scene()
            
            # 解析URDF
            self.robot = URDF.from_xml_file(urdf_path)
            self.urdf_dir = os.path.dirname(os.path.abspath(urdf_path))
            self.current_urdf_path = urdf_path
            
            # 加载所有链接的网格
            self._load_link_meshes()
            
            # 重置相机视角
            self.reset_camera()
            
            # 更新信息显示
            self.info_label.setText(f"已加载: {os.path.basename(urdf_path)}\n链接数: {len(self.robot.links)}")
            
            print(f"URDF加载完成，共{len(self.robot.links)}个链接")
            self.loading_finished.emit()
            
        except Exception as e:
            print(f"加载URDF失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _load_link_meshes(self):
        """加载所有链接的网格"""
        for link in self.robot.links:
            if link.visuals:
                for i, visual in enumerate(link.visuals):
                    if visual.geometry and hasattr(visual.geometry, 'filename'):
                        mesh_path = self._resolve_mesh_path(visual.geometry.filename)
                        if os.path.exists(mesh_path):
                            actor = self._create_mesh_actor(mesh_path, visual)
                            if actor:
                                actor_name = f"{link.name}_{i}" if len(link.visuals) > 1 else link.name
                                self.mesh_actors[actor_name] = {
                                    'actor': actor,
                                    'link': link,
                                    'visual': visual
                                }
                                self.renderer.AddActor(actor)
    
    def _resolve_mesh_path(self, filename):
        """解析网格文件路径"""
        if filename.startswith('package://'):
            # 处理ROS包路径
            package_path = filename.replace('package://', '')
            parts = package_path.split('/', 1)
            if len(parts) == 2:
                package_name, relative_path = parts
                return os.path.join(self.urdf_dir, '..', package_name, relative_path)
        elif filename.startswith('file://'):
            return filename[7:]
        elif not os.path.isabs(filename):
            return os.path.join(self.urdf_dir, filename)
        return filename

    def _create_mesh_actor(self, mesh_path, visual):
        """创建网格Actor"""
        try:
            # 使用trimesh加载网格
            mesh = trimesh.load(mesh_path)

            # 创建VTK点和面
            points = vtk.vtkPoints()
            for vertex in mesh.vertices:
                points.InsertNextPoint(vertex)

            # 创建面
            polys = vtk.vtkCellArray()
            for face in mesh.faces:
                polys.InsertNextCell(len(face))
                for vertex_id in face:
                    polys.InsertCellPoint(vertex_id)

            # 创建PolyData
            polydata = vtk.vtkPolyData()
            polydata.SetPoints(points)
            polydata.SetPolys(polys)

            # 计算法向量
            normals = vtk.vtkPolyDataNormals()
            normals.SetInputData(polydata)
            normals.ComputePointNormalsOn()
            normals.ComputeCellNormalsOn()
            normals.Update()

            # 创建Mapper
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(normals.GetOutputPort())

            # 创建Actor
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)

            # 设置材质属性
            self._apply_material(actor, visual)

            # 应用变换
            if visual.origin:
                transform = vtk.vtkTransform()
                if visual.origin.xyz:
                    transform.Translate(*visual.origin.xyz)
                if visual.origin.rpy:
                    r, p, y = visual.origin.rpy
                    transform.RotateZ(np.degrees(y))
                    transform.RotateY(np.degrees(p))
                    transform.RotateX(np.degrees(r))
                actor.SetUserTransform(transform)

            return actor

        except Exception as e:
            print(f"创建网格Actor失败 {mesh_path}: {e}")
            return None

    def _apply_material(self, actor, visual):
        """应用材质属性"""
        property = actor.GetProperty()

        # 默认机器人材质
        property.SetColor(0.7, 0.7, 0.8)  # 银灰色
        property.SetAmbient(0.3)
        property.SetDiffuse(0.7)
        property.SetSpecular(0.3)
        property.SetSpecularPower(20)
        property.SetMetallic(0.8)
        property.SetRoughness(0.2)

        # 如果有材质定义，覆盖颜色
        if visual.material and visual.material.color:
            rgba = visual.material.color.rgba
            if rgba and len(rgba) >= 3:
                property.SetColor(rgba[0], rgba[1], rgba[2])
                if len(rgba) >= 4:
                    property.SetOpacity(rgba[3])

    def clear_scene(self):
        """清除场景中的所有网格"""
        for actor_info in self.mesh_actors.values():
            self.renderer.RemoveActor(actor_info['actor'])
        self.mesh_actors.clear()
        if self.render_window:
            self.render_window.Render()

    def reset_camera(self):
        """重置相机视角"""
        if not self.renderer:
            return

        self.renderer.ResetCamera()

        # 获取场景边界
        bounds = self.renderer.ComputeVisiblePropBounds()
        if bounds[0] < bounds[1]:  # 有有效边界
            center = [(bounds[0] + bounds[1]) / 2,
                     (bounds[2] + bounds[3]) / 2,
                     (bounds[4] + bounds[5]) / 2]

            # 计算场景大小
            size = max(bounds[1] - bounds[0],
                      bounds[3] - bounds[2],
                      bounds[5] - bounds[4])

            # 设置相机位置
            camera = self.renderer.GetActiveCamera()
            camera.SetPosition(center[0] + size * 1.5,
                             center[1] + size * 1.5,
                             center[2] + size * 1)
            camera.SetFocalPoint(*center)
            camera.SetViewUp(0, 0, 1)
        else:
            # 默认视角
            self.renderer.GetActiveCamera().SetPosition(0.3, 0.3, 0.2)
            self.renderer.GetActiveCamera().SetFocalPoint(0, 0, 0.05)
            self.renderer.GetActiveCamera().SetViewUp(0, 0, 1)

        if self.render_window:
            self.render_window.Render()

    def open_new_window(self):
        """打开新的URDF查看器窗口"""
        try:
            # 释放当前上下文
            VTKContextManager.release_context(self.render_window)

            # 强制处理所有待处理的事件
            QApplication.processEvents()

            # 创建新的查看器实例
            new_viewer = FixedVTKURDFViewer()

            # 设置新窗口的位置，避免重叠
            if hasattr(self, 'pos'):
                current_pos = self.pos()
                new_viewer.move(current_pos.x() + 50, current_pos.y() + 50)

            new_viewer.setWindowTitle("URDF查看器 - 新窗口")

            # 如果当前有模型，询问是否加载相同模型
            if self.robot and self.current_urdf_path:
                # 延迟加载，确保窗口完全初始化
                QTimer.singleShot(20, lambda: new_viewer.load_urdf(self.current_urdf_path))

            # 显示新窗口
            new_viewer.show()
            new_viewer.raise_()
            new_viewer.activateWindow()

            # 将新窗口添加到窗口列表中（避免被垃圾回收）
            self.additional_windows.append(new_viewer)

            print("已打开新的URDF查看器窗口")

        except Exception as e:
            print(f"创建新窗口失败: {e}")
            import traceback
            traceback.print_exc()

    def cleanup_resources(self):
        """清理VTK资源"""
        try:
            # 清理场景
            self.clear_scene()

            # 释放OpenGL上下文
            if hasattr(self, 'render_window') and self.render_window:
                VTKContextManager.release_context(self.render_window)

            # 清理VTK组件
            if hasattr(self, 'interactor') and self.interactor:
                self.interactor.TerminateApp()

            if hasattr(self, 'render_window') and self.render_window:
                self.render_window.Finalize()

            # 清理引用
            self.vtk_widget = None
            self.renderer = None
            self.render_window = None
            self.interactor = None

        except Exception as e:
            print(f"清理VTK资源时出错: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            self.cleanup_resources()
            # 清理额外窗口
            for window in self.additional_windows:
                try:
                    window.cleanup_resources()
                    window.close()
                except:
                    pass
            event.accept()
        except Exception as e:
            print(f"关闭窗口时出错: {e}")
            event.accept()


class TestFixedVTKWindow(QMainWindow):
    """测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("修复版VTK URDF查看器")
        self.setGeometry(100, 100, 1000, 700)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # 创建VTK URDF查看器
        self.vtk_viewer = FixedVTKURDFViewer()
        layout.addWidget(self.vtk_viewer)

        # 连接信号
        self.vtk_viewer.loading_finished.connect(self.on_loading_finished)

    def on_loading_finished(self):
        print("URDF加载完成！")

    def closeEvent(self, event):
        """关闭时清理VTK资源"""
        if hasattr(self, 'vtk_viewer'):
            self.vtk_viewer.cleanup_resources()
        event.accept()


def main():
    app = QApplication(sys.argv)

    window = TestFixedVTKWindow()
    window.show()

    print("修复版VTK URDF查看器启动")
    print("点击'加载URDF'按钮加载模型")
    print("点击'新窗口'按钮测试多窗口功能")

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
